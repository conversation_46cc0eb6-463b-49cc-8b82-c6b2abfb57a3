<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    header("location: ../index.php");
    exit;
}

require_once 'config.php';
require_once 'functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credit Management - Admin Dashboard</title>
    <link rel="stylesheet" href="../CSS/custom-modal.css" />
    <style>
        body { font-family: Arial, sans-serif; background-color: #1a1a1a; color: white; margin: 0; padding: 20px; }
        .container { max-width: 1400px; margin: auto; background-color: #2c2c2c; padding: 30px; border-radius: 15px; }
        h1, h2 { color: #ff4500; text-align: center; }
        
        /* Navigation */
        .nav-controls { text-align: center; margin-bottom: 30px; }
        .nav-controls button { padding: 10px 20px; margin: 0 10px; border: none; border-radius: 5px; background-color: #008B8B; color: white; cursor: pointer; }
        .nav-controls button:hover { background-color: #20B2AA; }
        .nav-controls button.active { background-color: #ff4500; }
        
        /* Stats Cards */
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background-color: #333; padding: 20px; border-radius: 10px; text-align: center; border-left: 5px solid #ff4500; }
        .stat-card h3 { margin: 0 0 10px 0; color: #00cyan; font-size: 16px; }
        .stat-value { font-size: 28px; font-weight: bold; color: #32cd32; }
        .stat-label { font-size: 12px; color: #ccc; margin-top: 5px; }
        
        /* Tables */
        .table-container { background-color: #333; border-radius: 10px; padding: 20px; margin-bottom: 30px; }
        .table-container h3 { color: #00cyan; margin-top: 0; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #555; }
        th { background-color: #444; color: #ff4500; font-weight: bold; }
        tr:hover { background-color: #3a3a3a; }
        
        /* Buttons */
        .btn { padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px; margin: 2px; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-success:hover { background-color: #218838; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-danger:hover { background-color: #c82333; }
        .btn-info { background-color: #17a2b8; color: white; }
        .btn-info:hover { background-color: #138496; }
        .btn-warning { background-color: #ffc107; color: #333; }
        .btn-warning:hover { background-color: #e0a800; }
        
        /* Status badges */
        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: bold; }
        .status-pending { background-color: #ffc107; color: #333; }
        .status-processing { background-color: #17a2b8; color: white; }
        .status-completed { background-color: #28a745; color: white; }
        .status-failed { background-color: #dc3545; color: white; }
        .status-cancelled { background-color: #6c757d; color: white; }
        
        /* Forms */
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; color: #ccc; }
        .form-group input, .form-group select, .form-group textarea { 
            width: 100%; padding: 8px; border-radius: 5px; border: 1px solid #666; 
            background-color: #444; color: white; 
        }
        
        /* Modals */
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8); }
        .modal-content { background-color: #2c2c2c; margin: 5% auto; padding: 30px; border-radius: 15px; width: 90%; max-width: 600px; }
        .modal h3 { color: #ff4500; text-align: center; margin-bottom: 20px; }
        .close { position: absolute; right: 15px; top: 15px; font-size: 28px; font-weight: bold; color: #ccc; cursor: pointer; }
        .close:hover { color: #ff4500; }
        
        /* Logout button */
        .logout-btn {
            position: fixed; top: 15px; right: 15px; background-color: #e74c3c; color: white;
            border: none; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-size: 14px;
        }
        .logout-btn:hover { background-color: #c0392b; }
        
        /* Pagination */
        .pagination { text-align: center; margin-top: 20px; }
        .pagination button { padding: 8px 12px; margin: 0 2px; border: none; border-radius: 5px; background-color: #444; color: white; cursor: pointer; }
        .pagination button:hover { background-color: #555; }
        .pagination button.active { background-color: #ff4500; }
        
        /* Filters */
        .filters { display: flex; gap: 15px; margin-bottom: 20px; align-items: end; }
        .filters .form-group { margin-bottom: 0; min-width: 150px; }
        
        /* Hidden sections */
        .section { display: none; }
        .section.active { display: block; }
    </style>
</head>
<body>
    <!-- Logout button -->
    <button class="logout-btn" onclick="confirmLogout()">Logout</button>

    <div class="container">
        <h1>💰 KMS Credit Management Dashboard</h1>
        
        <!-- Navigation -->
        <div class="nav-controls">
            <button onclick="showSection('overview')" class="nav-btn active" data-section="overview">📊 Overview</button>
            <button onclick="showSection('wallets')" class="nav-btn" data-section="wallets">👥 User Wallets</button>
            <button onclick="showSection('deposits')" class="nav-btn" data-section="deposits">💳 Pending Deposits</button>
            <button onclick="showSection('transactions')" class="nav-btn" data-section="transactions">📋 All Transactions</button>
            <button onclick="showSection('settings')" class="nav-btn" data-section="settings">⚙️ Settings</button>
            <button onclick="location.href='admin.php'" style="background-color: #6c757d;">← Back to Admin</button>
        </div>

        <!-- Overview Section -->
        <div id="overview" class="section active">
            <h2>System Overview</h2>
            <div class="stats-grid" id="statsGrid">
                <!-- Stats will be loaded here -->
            </div>
        </div>

        <!-- User Wallets Section -->
        <div id="wallets" class="section">
            <div class="table-container">
                <h3>User Wallets</h3>
                <div class="filters">
                    <div class="form-group">
                        <label>Search User:</label>
                        <input type="text" id="walletSearch" placeholder="Username or email">
                    </div>
                    <div class="form-group">
                        <button class="btn btn-info" onclick="loadWallets()">Search</button>
                    </div>
                </div>
                <div id="walletsTable">
                    <!-- Wallets table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Pending Deposits Section -->
        <div id="deposits" class="section">
            <div class="table-container">
                <h3>Pending Deposits</h3>
                <div id="depositsTable">
                    <!-- Deposits table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- All Transactions Section -->
        <div id="transactions" class="section">
            <div class="table-container">
                <h3>All Transactions</h3>
                <div class="filters">
                    <div class="form-group">
                        <label>Type:</label>
                        <select id="transactionTypeFilter">
                            <option value="">All Types</option>
                            <option value="deposit">Deposit</option>
                            <option value="withdraw">Withdraw</option>
                            <option value="spend">Spend</option>
                            <option value="refund">Refund</option>
                            <option value="transfer_in">Transfer In</option>
                            <option value="transfer_out">Transfer Out</option>
                            <option value="admin_adjust">Admin Adjust</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Status:</label>
                        <select id="transactionStatusFilter">
                            <option value="">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="completed">Completed</option>
                            <option value="failed">Failed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-info" onclick="loadTransactions()">Filter</button>
                    </div>
                </div>
                <div id="transactionsTable">
                    <!-- Transactions table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Settings Section -->
        <div id="settings" class="section">
            <div class="table-container">
                <h3>Payment Methods</h3>
                <div id="paymentMethodsTable">
                    <!-- Payment methods will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Approve Deposit Modal -->
    <div id="approveDepositModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('approveDepositModal')">&times;</span>
            <h3>Approve Deposit</h3>
            <form id="approveDepositForm">
                <input type="hidden" id="approveTransactionId">
                <div class="form-group">
                    <label>Admin Notes:</label>
                    <textarea id="approveNotes" rows="3" placeholder="Optional notes about this approval"></textarea>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-success" style="width: 100%;">Approve Deposit</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Reject Deposit Modal -->
    <div id="rejectDepositModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('rejectDepositModal')">&times;</span>
            <h3>Reject Deposit</h3>
            <form id="rejectDepositForm">
                <input type="hidden" id="rejectTransactionId">
                <div class="form-group">
                    <label>Reason for Rejection:</label>
                    <textarea id="rejectNotes" rows="3" placeholder="Please provide reason for rejection" required></textarea>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-danger" style="width: 100%;">Reject Deposit</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Manual Adjust Modal -->
    <div id="adjustBalanceModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('adjustBalanceModal')">&times;</span>
            <h3>Manual Balance Adjustment</h3>
            <form id="adjustBalanceForm">
                <input type="hidden" id="adjustUserId">
                <div class="form-group">
                    <label>Username:</label>
                    <input type="text" id="adjustUsername" readonly>
                </div>
                <div class="form-group">
                    <label>Adjustment Amount (+ for add, - for deduct):</label>
                    <input type="number" id="adjustAmount" step="0.01" required>
                </div>
                <div class="form-group">
                    <label>Reason:</label>
                    <textarea id="adjustReason" rows="3" placeholder="Reason for this adjustment" required></textarea>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-warning" style="width: 100%;">Apply Adjustment</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../JS/custom-modal.js"></script>
    <script>
        // Global variables
        let currentSection = 'overview';
        let currentPage = 1;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStats();
            loadPendingDeposits();
        });
        
        // Section navigation
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Remove active class from all nav buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionName).classList.add('active');
            document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
            
            currentSection = sectionName;
            
            // Load section data
            switch(sectionName) {
                case 'overview':
                    loadSystemStats();
                    break;
                case 'wallets':
                    loadWallets();
                    break;
                case 'deposits':
                    loadPendingDeposits();
                    break;
                case 'transactions':
                    loadTransactions();
                    break;
                case 'settings':
                    loadPaymentMethods();
                    break;
            }
        }
        
        // Load system statistics
        function loadSystemStats() {
            fetch('admin_credit.php?action=get_system_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        document.getElementById('statsGrid').innerHTML = `
                            <div class="stat-card">
                                <h3>Total Users</h3>
                                <div class="stat-value">${stats.total_users}</div>
                                <div class="stat-label">Active wallets</div>
                            </div>
                            <div class="stat-card">
                                <h3>Total Balance</h3>
                                <div class="stat-value">$${stats.total_balance}</div>
                                <div class="stat-label">In system</div>
                            </div>
                            <div class="stat-card">
                                <h3>Total Deposits</h3>
                                <div class="stat-value">$${stats.total_deposits}</div>
                                <div class="stat-label">All time</div>
                            </div>
                            <div class="stat-card">
                                <h3>Total Spent</h3>
                                <div class="stat-value">$${stats.total_spent}</div>
                                <div class="stat-label">All time</div>
                            </div>
                            <div class="stat-card">
                                <h3>Pending Deposits</h3>
                                <div class="stat-value">${stats.pending_deposits_count}</div>
                                <div class="stat-label">$${stats.pending_deposits_amount}</div>
                            </div>
                            <div class="stat-card">
                                <h3>Recent Activity</h3>
                                <div class="stat-value">${stats.recent_transactions}</div>
                                <div class="stat-label">Last 7 days</div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error loading stats:', error);
                });
        }
        
        // Logout function
        function confirmLogout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = 'logout.php';
            }
        }
        
        // Modal functions
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // Load user wallets
        function loadWallets() {
            const search = document.getElementById('walletSearch').value;
            const params = new URLSearchParams({
                action: 'get_all_wallets',
                page: currentPage,
                limit: 20
            });

            if (search) {
                params.append('search', search);
            }

            fetch(`admin_credit.php?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = `
                            <table>
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Email</th>
                                        <th>Balance</th>
                                        <th>Frozen</th>
                                        <th>Total Deposited</th>
                                        <th>Total Spent</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        data.wallets.forEach(wallet => {
                            html += `
                                <tr>
                                    <td>${wallet.username}</td>
                                    <td>${wallet.email || 'N/A'}</td>
                                    <td>$${wallet.balance}</td>
                                    <td>$${wallet.frozen_balance}</td>
                                    <td>$${wallet.total_deposited}</td>
                                    <td>$${wallet.total_spent}</td>
                                    <td>
                                        <button class="btn btn-warning" onclick="openAdjustModal(${wallet.user_id}, '${wallet.username}')">Adjust</button>
                                    </td>
                                </tr>
                            `;
                        });

                        html += '</tbody></table>';
                        document.getElementById('walletsTable').innerHTML = html;
                    }
                })
                .catch(error => {
                    console.error('Error loading wallets:', error);
                });
        }

        // Load pending deposits
        function loadPendingDeposits() {
            fetch('admin_credit.php?action=get_pending_deposits')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = `
                            <table>
                                <thead>
                                    <tr>
                                        <th>Transaction ID</th>
                                        <th>User</th>
                                        <th>Amount</th>
                                        <th>Payment Method</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        data.pending_deposits.forEach(deposit => {
                            html += `
                                <tr>
                                    <td>${deposit.transaction_id}</td>
                                    <td>${deposit.username}</td>
                                    <td>$${deposit.amount}</td>
                                    <td>${deposit.payment_method}</td>
                                    <td><span class="status-badge status-${deposit.status}">${deposit.status}</span></td>
                                    <td>${deposit.created_at}</td>
                                    <td>
                                        <button class="btn btn-success" onclick="openApproveModal('${deposit.transaction_id}')">Approve</button>
                                        <button class="btn btn-danger" onclick="openRejectModal('${deposit.transaction_id}')">Reject</button>
                                    </td>
                                </tr>
                            `;
                        });

                        html += '</tbody></table>';
                        document.getElementById('depositsTable').innerHTML = html;
                    }
                })
                .catch(error => {
                    console.error('Error loading deposits:', error);
                });
        }

        // Load all transactions
        function loadTransactions() {
            const typeFilter = document.getElementById('transactionTypeFilter').value;
            const statusFilter = document.getElementById('transactionStatusFilter').value;

            const params = new URLSearchParams({
                action: 'get_all_transactions',
                page: currentPage,
                limit: 50
            });

            if (typeFilter) params.append('type', typeFilter);
            if (statusFilter) params.append('status', statusFilter);

            fetch(`admin_credit.php?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = `
                            <table>
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        data.transactions.forEach(transaction => {
                            html += `
                                <tr>
                                    <td>${transaction.transaction_id}</td>
                                    <td>${transaction.username}</td>
                                    <td>${transaction.type}</td>
                                    <td>$${transaction.amount}</td>
                                    <td><span class="status-badge status-${transaction.status}">${transaction.status}</span></td>
                                    <td>${transaction.created_at}</td>
                                    <td>${transaction.description || 'N/A'}</td>
                                </tr>
                            `;
                        });

                        html += '</tbody></table>';
                        document.getElementById('transactionsTable').innerHTML = html;
                    }
                })
                .catch(error => {
                    console.error('Error loading transactions:', error);
                });
        }

        // Load payment methods
        function loadPaymentMethods() {
            // This would load payment methods configuration
            // For now, just show a placeholder
            document.getElementById('paymentMethodsTable').innerHTML = `
                <p>Payment methods configuration will be implemented here.</p>
                <p>Current active methods: PayPal, Stripe, Bank Transfer, Admin Manual</p>
            `;
        }

        // Modal functions
        function openApproveModal(transactionId) {
            document.getElementById('approveTransactionId').value = transactionId;
            document.getElementById('approveDepositModal').style.display = 'block';
        }

        function openRejectModal(transactionId) {
            document.getElementById('rejectTransactionId').value = transactionId;
            document.getElementById('rejectDepositModal').style.display = 'block';
        }

        function openAdjustModal(userId, username) {
            document.getElementById('adjustUserId').value = userId;
            document.getElementById('adjustUsername').value = username;
            document.getElementById('adjustBalanceModal').style.display = 'block';
        }

        // Form submissions
        document.getElementById('approveDepositForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'approve_deposit');
            formData.append('transaction_id', document.getElementById('approveTransactionId').value);
            formData.append('admin_notes', document.getElementById('approveNotes').value);

            fetch('admin_credit.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Deposit approved successfully!');
                    closeModal('approveDepositModal');
                    loadPendingDeposits();
                    loadSystemStats();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error approving deposit:', error);
                alert('Network error occurred');
            });
        });

        document.getElementById('rejectDepositForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'reject_deposit');
            formData.append('transaction_id', document.getElementById('rejectTransactionId').value);
            formData.append('admin_notes', document.getElementById('rejectNotes').value);

            fetch('admin_credit.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Deposit rejected successfully!');
                    closeModal('rejectDepositModal');
                    loadPendingDeposits();
                    loadSystemStats();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error rejecting deposit:', error);
                alert('Network error occurred');
            });
        });

        document.getElementById('adjustBalanceForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'manual_adjust');
            formData.append('user_id', document.getElementById('adjustUserId').value);
            formData.append('amount', document.getElementById('adjustAmount').value);
            formData.append('reason', document.getElementById('adjustReason').value);

            fetch('admin_credit.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Balance adjusted successfully!');
                    closeModal('adjustBalanceModal');
                    loadWallets();
                    loadSystemStats();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error adjusting balance:', error);
                alert('Network error occurred');
            });
        });

        // Click outside modal to close
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
