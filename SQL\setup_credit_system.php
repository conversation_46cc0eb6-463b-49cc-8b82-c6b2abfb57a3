<?php
/**
 * Setup script for KMS Credit System
 * This script creates all necessary tables and initial data
 */

require_once __DIR__ . '/../PHP/config.php';

echo "<h1>KMS Credit System Setup</h1>";

// Read and execute the SQL file
$sql_file = 'kms_credit_system.sql';
$sql_content = file_get_contents($sql_file);

if ($sql_content === false) {
    die("Error: Could not read SQL file: $sql_file");
}

// Remove comments and split into individual statements
$sql_statements = [];
$lines = explode("\n", $sql_content);
$current_statement = '';

foreach ($lines as $line) {
    $line = trim($line);
    
    // Skip empty lines and comments
    if (empty($line) || strpos($line, '--') === 0) {
        continue;
    }
    
    // Handle DELIMITER changes
    if (strpos($line, 'DELIMITER') === 0) {
        if (strpos($line, '//') !== false) {
            // Start of stored procedure/trigger
            $current_statement .= $line . "\n";
            continue;
        } else {
            // End of stored procedure/trigger
            if (!empty($current_statement)) {
                $sql_statements[] = trim($current_statement);
                $current_statement = '';
            }
            continue;
        }
    }
    
    $current_statement .= $line . "\n";
    
    // Check if statement ends with semicolon
    if (substr(rtrim($line), -1) === ';') {
        $sql_statements[] = trim($current_statement);
        $current_statement = '';
    }
}

// Add any remaining statement
if (!empty($current_statement)) {
    $sql_statements[] = trim($current_statement);
}

echo "<h2>Executing SQL Statements...</h2>";

$success_count = 0;
$error_count = 0;

foreach ($sql_statements as $index => $statement) {
    if (empty(trim($statement))) {
        continue;
    }
    
    echo "<p><strong>Statement " . ($index + 1) . ":</strong> ";
    
    // Handle special cases
    if (strpos($statement, 'USE ') === 0) {
        echo "Selecting database... ";
        if (mysqli_query($link, $statement)) {
            echo "<span style='color: green;'>✓ Success</span></p>";
            $success_count++;
        } else {
            echo "<span style='color: red;'>✗ Error: " . mysqli_error($link) . "</span></p>";
            $error_count++;
        }
        continue;
    }
    
    if (strpos($statement, 'CREATE TABLE') !== false) {
        preg_match('/CREATE TABLE[^`]*`?([^`\s]+)`?/', $statement, $matches);
        $table_name = $matches[1] ?? 'unknown';
        echo "Creating table '$table_name'... ";
    } elseif (strpos($statement, 'INSERT INTO') !== false) {
        preg_match('/INSERT INTO[^`]*`?([^`\s]+)`?/', $statement, $matches);
        $table_name = $matches[1] ?? 'unknown';
        echo "Inserting data into '$table_name'... ";
    } elseif (strpos($statement, 'CREATE INDEX') !== false) {
        echo "Creating index... ";
    } elseif (strpos($statement, 'CREATE TRIGGER') !== false) {
        echo "Creating trigger... ";
    } elseif (strpos($statement, 'CREATE OR REPLACE VIEW') !== false) {
        preg_match('/CREATE OR REPLACE VIEW[^`]*`?([^`\s]+)`?/', $statement, $matches);
        $view_name = $matches[1] ?? 'unknown';
        echo "Creating view '$view_name'... ";
    } else {
        echo "Executing statement... ";
    }
    
    if (mysqli_query($link, $statement)) {
        echo "<span style='color: green;'>✓ Success</span></p>";
        $success_count++;
    } else {
        echo "<span style='color: red;'>✗ Error: " . mysqli_error($link) . "</span></p>";
        $error_count++;
    }
}

echo "<h2>Setup Summary</h2>";
echo "<p><strong>Total Statements:</strong> " . count($sql_statements) . "</p>";
echo "<p><strong>Successful:</strong> <span style='color: green;'>$success_count</span></p>";
echo "<p><strong>Errors:</strong> <span style='color: red;'>$error_count</span></p>";

if ($error_count == 0) {
    echo "<h3 style='color: green;'>🎉 KMS Credit System setup completed successfully!</h3>";
    
    // Verify tables were created
    echo "<h3>Verifying Tables...</h3>";
    $tables_to_check = [
        'user_wallets',
        'credit_transactions', 
        'deposit_records',
        'transfer_records',
        'payment_methods',
        'credit_system_settings'
    ];
    
    foreach ($tables_to_check as $table) {
        $result = mysqli_query($link, "SHOW TABLES LIKE '$table'");
        if (mysqli_num_rows($result) > 0) {
            echo "<p>✓ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '$table' not found</p>";
        }
    }
    
    // Check if wallets were created for existing users
    $result = mysqli_query($link, "SELECT COUNT(*) as count FROM user_wallets");
    $wallet_count = mysqli_fetch_assoc($result)['count'];
    echo "<p>✓ Created $wallet_count user wallets</p>";
    
    // Check payment methods
    $result = mysqli_query($link, "SELECT COUNT(*) as count FROM payment_methods WHERE is_active = 1");
    $method_count = mysqli_fetch_assoc($result)['count'];
    echo "<p>✓ Configured $method_count payment methods</p>";
    
} else {
    echo "<h3 style='color: red;'>⚠️ Setup completed with errors. Please check the error messages above.</h3>";
}

mysqli_close($link);
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
p { margin: 5px 0; }
</style>
