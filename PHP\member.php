<?php
require_once 'config.php';
require_once 'language.php';

// Check if user is logged in
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: ../index.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= t('member_page_title') ?></title>
    <link rel="stylesheet" href="../CSS/custom-modal.css" />
    <style>
        body { font-family: Arial, sans-serif; background-color: #a48f19; color: white; margin: 0; padding: 20px; }
        .container { max-width: 1000px; margin: auto; background-color: #2b9869; padding: 30px; border-radius: 14px; box-shadow: 0 5px 15px rgba(0,0,0,0.5); }
        h1, h2 { color: #00cyan; text-align: center; }
        #welcome-msg { text-align: center; font-size: 24px; margin-bottom: 20px; }
        .order-form {text-align: center; margin-top: 20px; }
        .service-item { background-color: #2266a8; padding: 15px; border-radius: 12px; margin-bottom: 15px; display: flex; align-items: center; }
        .service-item input { margin-right: 15px; width: 20px; height: 20px; }
        .service-item label { font-size: 18px; flex-grow: 1; }
        #order-notes { width: calc(100% - 22px); height: 80px; padding: 10px; margin-top: 10px; border-radius: 12px; background-color: #1f5478; color: white; border: 2px solid #1585cf; font-size: 24px; }
        button { width: 200px; padding: 15px; border: none; border-radius: 12px; background-color:rgb(232, 186, 0); color: white; font-size: 22px; text-align: center; cursor: pointer; margin-top: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.5);}
        button:hover { background-color: #20B2AA; }

        /* Logout button positioning */
        .logout-btn {
            position: fixed;
            bottom: 25px;
            right: 35px;
            width: 100px;
            background-color:rgb(255, 25, 0);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 22px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 500;
            z-index: 1000;
            transition: background-color 0.3s ease;
        }
        .logout-btn:hover { background-color: #c0392b; }

        /* VIP PC Customization */
        .vip-config-container {
            background-color: rgba(0,0,0,0.2);
            padding: 20px;
            margin-top: -15px; /* Pull it up to align with the bottom of the checkbox item */
            margin-left: 20px;
            margin-right: 20px;
            margin-bottom: 15px;
            border-radius: 0 0 14px 14px;
            border: 1px solid #1585cf;
            border-top: none;
        }
        .mode-selection { text-align: center; margin-bottom: 20px; }
        .mode-selection h4 {
            margin-bottom: 10px;
            color: #ffd700;
        }
        .mode-btn { margin: 0 10px; }
        .mode-btn.active {
            background-color: #ffd700;
            color: #1a1a1a;
            box-shadow: 0 0 8px #ffd700;
        }
        .mode-btn:disabled {
            background-color: #555;
            cursor: not-allowed;
        }
        .options-content .option-group {
            margin-bottom: 15px;
            padding: 10px;
            background-color: rgba(255,255,255,0.05);
            border-radius: 8px;
        }
        .options-content .option-group h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #ffffff;
            font-size: 16px;
            border-bottom: 1px solid #1585cf;
            padding-bottom: 5px;
        }
        .options-content .radio-group label {
            display: inline-block;
            margin-right: 20px;
            cursor: pointer;
        }
        .options-content .radio-group input {
            margin-right: 5px;
            vertical-align: middle;
        }

        /* Orders History Styles */
        #orderHistory { margin-top: 40px; }
        .order-card { background-color: rgba(0,0,0,0.25); border: 1px solid #1585cf; padding: 20px; border-radius: 12px; margin-bottom: 20px; }
        .order-card h4 { margin-top: 0; color: #ffd700; border-bottom: 1px solid #555; padding-bottom: 10px; }
        .order-actions { margin-top: 20px; text-align: right; }
        .order-actions button { margin-left: 10px; padding: 10px 20px; font-size: 16px; width: auto; background-color: #dc3545; }
        .order-actions button:hover { background-color: #c82333; }
        .order-status { font-weight: bold; padding: 5px 10px; border-radius: 5px; color: white; }
        .order-status.pending { background-color: #ffc107; color: #333; }
        .order-status.processing { background-color: #007bff; }
        .order-status.completed { background-color: #28a745; }
        .order-status.cancelled { background-color: #dc3545; }
        .vip-details { margin-top: 10px; padding: 10px; background-color: rgba(255,255,255,0.05); border-radius: 8px; }
        .vip-details ul { margin: 5px 0; padding-left: 20px; }

        /* PC Orders History Styles */
        #pcOrdersContainer { margin-top: 40px; }
        .pc-order-card { background-color: rgba(0,0,0,0.25); border: 1px solid #1585cf; padding: 20px; border-radius: 12px; margin-bottom: 20px; }
        .pc-order-card h4 { margin-top: 0; color: #ffd700; border-bottom: 1px solid #555; padding-bottom: 10px; }
        .pc-order-details-grid { display: grid; grid-template-columns: 1fr 2fr; gap: 10px 20px; }
        .pc-order-details-grid p { margin: 5px 0; }
        .pc-order-details-grid strong { color: #ccc; }
        .pc-order-notices {
            margin-top: 15px;
            padding: 10px;
            border-radius: 8px;
            font-size: 14px;
            text-align: center;
        }
        .notice-warning { background-color: rgba(255, 193, 7, 0.2); border: 1px solid #ffc107; color: #ffc107; }
        .notice-info { background-color: rgba(23, 162, 184, 0.2); border: 1px solid #17a2b8; color: #17a2b8; }
        .pc-order-actions { margin-top: 20px; text-align: right; }
        .pc-order-actions button { margin-left: 10px; padding: 10px 20px; font-size: 16px; width: auto; }
        .pc-order-actions button.cancel-btn { background-color: #dc3545; }
        .pc-order-actions button.cancel-btn:hover { background-color: #c82333; }
        .pc-order-actions button.confirm-btn { background-color: #28a745; }
        .pc-order-actions button.confirm-btn:hover { background-color: #218838; }
        .pc-order-status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 5px;
            color: white;
        }
        .pc-order-status.status-quote-requested { background-color: #6c757d; }
        .pc-order-status.status-quote-provided { background-color: #17a2b8; }
        .pc-order-status.status-order-pending { background-color: #ffc107; color: #333; }
        .pc-order-status.status-payment-received, .pc-order-status.status-order-in-progress { background-color: #007bff; }
        .pc-order-status.status-order-shipped { background-color: #fd7e14; }
        .pc-order-status.status-order-delivered { background-color: #28a745; }
        .pc-order-status.status-order-cancelled { background-color: #dc3545; }

        /* KMS Credit Wallet Styles */
        .wallet-section { margin-top: 40px; }
        .wallet-card { background-color: rgba(0,0,0,0.25); border: 1px solid #1585cf; padding: 20px; border-radius: 12px; margin-bottom: 20px; }
        .wallet-balance { text-align: center; margin-bottom: 20px; }
        .wallet-balance h3 { color: #ffd700; font-size: 28px; margin-bottom: 10px; }
        .balance-amount { font-size: 36px; color: #00ff00; font-weight: bold; }
        .wallet-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-item { text-align: center; padding: 10px; background-color: rgba(255,255,255,0.05); border-radius: 8px; }
        .stat-item h4 { margin: 0 0 5px 0; color: #ccc; font-size: 14px; }
        .stat-item .stat-value { color: #fff; font-size: 18px; font-weight: bold; }
        .wallet-actions { display: flex; gap: 10px; justify-content: center; flex-wrap: wrap; }
        .wallet-actions button { width: auto; min-width: 120px; }
        .deposit-btn { background-color: #28a745 !important; }
        .deposit-btn:hover { background-color: #218838 !important; }
        .transfer-btn { background-color: #17a2b8 !important; }
        .transfer-btn:hover { background-color: #138496 !important; }
        .history-btn { background-color: #6c757d !important; }
        .history-btn:hover { background-color: #5a6268 !important; }

        /* Transaction History Styles */
        .transaction-card { background-color: rgba(0,0,0,0.15); border: 1px solid #444; padding: 15px; border-radius: 8px; margin-bottom: 10px; }
        .transaction-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .transaction-type { font-weight: bold; color: #ffd700; }
        .transaction-amount { font-weight: bold; font-size: 18px; }
        .transaction-amount.positive { color: #28a745; }
        .transaction-amount.negative { color: #dc3545; }
        .transaction-details { font-size: 14px; color: #ccc; }
        .transaction-date { font-size: 12px; color: #999; }

        /* Modal Styles for Credit System */
        .credit-modal { display: none; position: fixed; z-index: 2000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8); }
        .credit-modal-content { background-color: #2b9869; margin: 5% auto; padding: 30px; border-radius: 14px; width: 90%; max-width: 600px; position: relative; }
        .credit-modal h3 { color: #ffd700; text-align: center; margin-bottom: 20px; }
        .credit-form-group { margin-bottom: 20px; }
        .credit-form-group label { display: block; margin-bottom: 5px; color: #fff; font-weight: bold; }
        .credit-form-group input, .credit-form-group select, .credit-form-group textarea {
            width: calc(100% - 22px); padding: 10px; border-radius: 8px; border: 2px solid #1585cf;
            background-color: #1f5478; color: white; font-size: 16px;
        }
        .credit-form-group input:focus, .credit-form-group select:focus, .credit-form-group textarea:focus {
            outline: none; border-color: #ffd700; box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
        }
        .payment-method-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .payment-method-option { padding: 15px; background-color: rgba(255,255,255,0.05); border: 2px solid #1585cf; border-radius: 8px; cursor: pointer; text-align: center; transition: all 0.3s; }
        .payment-method-option:hover, .payment-method-option.selected { border-color: #ffd700; background-color: rgba(255, 215, 0, 0.1); }
        .close-credit-modal { position: absolute; right: 15px; top: 15px; font-size: 28px; font-weight: bold; color: #fff; cursor: pointer; }
        .close-credit-modal:hover { color: #ffd700; }
    </style>
</head>
<body>
    <!-- Logout button in top right -->
    <button class="logout-btn" id="logoutBtn"><?= t('logout_button') ?></button>

    <div class="container">
        <h1 id="welcome-msg"><?= str_replace('{username}', htmlspecialchars($_SESSION["username"]), t('welcome_message')) ?></h1>

        <!-- KMS Credit Wallet Section -->
        <div class="wallet-section">
            <h2 style="text-align: center; color: #ffd700;">💰 KMS Credit Wallet</h2>
            <div class="wallet-card">
                <div class="wallet-balance">
                    <h3>Available Balance</h3>
                    <div class="balance-amount" id="walletBalance">$0.00</div>
                </div>

                <div class="wallet-stats">
                    <div class="stat-item">
                        <h4>Total Deposited</h4>
                        <div class="stat-value" id="totalDeposited">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Total Spent</h4>
                        <div class="stat-value" id="totalSpent">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Frozen Balance</h4>
                        <div class="stat-value" id="frozenBalance">$0.00</div>
                    </div>
                </div>

                <div class="wallet-actions">
                    <button class="deposit-btn" onclick="openDepositModal()">💳 Deposit</button>
                    <button class="transfer-btn" onclick="openTransferModal()">💸 Transfer</button>
                    <button class="history-btn" onclick="openTransactionHistory()">📊 History</button>
                </div>
            </div>
        </div>

        <h2><?= t('service_orders_title') ?></h2>
        <p style="text-align: center;"><?= t('service_selection_prompt') ?></p>
        
        <form id="orderForm" class="order-form">
            <div class="service-item">
                <input type="checkbox" id="service1" name="service" value="VIP Custom PC">
                <label for="service1"><?= t('service_vip_pc') ?></label>
            </div>
            <div class="vip-config-container" id="vipConfigContainer" style="display: none;">
                <div class="mode-selection">
                    <h4><?= t('vip_pc_select_config_mode') ?></h4>
                    <button type="button" class="mode-btn" data-mode="simple"><?= t('vip_pc_simple_mode_button') ?></button>
                    <button type="button" class="mode-btn" data-mode="detailed" disabled><?= t('vip_pc_detailed_mode_button') ?></button>
                </div>
                <div id="vipOptionsContent" class="options-content"></div>
            </div>
            <div class="service-item">
                <input type="checkbox" id="service2" name="service" value="Optimize Photo & Video">
                <label for="service2"><?= t('service_optimize_media') ?></label>
            </div>
            <div class="service-item">
                <input type="checkbox" id="service3" name="service" value="Print Service">
                <label for="service3"><?= t('service_print') ?></label>
            </div>
             <div class="service-item">
                <input type="checkbox" id="service5" name="service" value="Question Consult">
                <label for="service5"><?= t('service_consult') ?></label>
            </div>            
            <textarea id="order-notes" placeholder="<?= t('order_notes_placeholder') ?>"></textarea>
            <button type="submit"><?= t('confirm_order_button') ?></button>
        </form>

        <!-- Order History Section -->
        <div id="orderHistory" style="margin-top: 30px;">
            <h2><?= t('order_history_title') ?></h2>
            <div id="ordersList"></div>
        </div>

        <!-- PC Orders Section -->
        <div id="pcOrdersContainer">
            <h2><?= t('pc_orders_history_title') ?></h2>
            <div id="pcOrdersList"></div>
        </div>
    </div>

    <!-- Credit System Modals -->
    <!-- Deposit Modal -->
    <div id="depositModal" class="credit-modal">
        <div class="credit-modal-content">
            <span class="close-credit-modal" onclick="closeDepositModal()">&times;</span>
            <h3>💳 Deposit KMS Credit</h3>
            <form id="depositForm">
                <div class="credit-form-group">
                    <label for="depositAmount">Amount (USD):</label>
                    <input type="number" id="depositAmount" min="1" max="10000" step="0.01" required>
                </div>
                <div class="credit-form-group">
                    <label>Select Payment Method:</label>
                    <div class="payment-method-grid" id="paymentMethodGrid">
                        <!-- Payment methods will be loaded here -->
                    </div>
                </div>
                <div class="credit-form-group">
                    <button type="submit" style="width: 100%;">Create Deposit</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Transfer Modal -->
    <div id="transferModal" class="credit-modal">
        <div class="credit-modal-content">
            <span class="close-credit-modal" onclick="closeTransferModal()">&times;</span>
            <h3>💸 Transfer KMS Credit</h3>
            <form id="transferForm">
                <div class="credit-form-group">
                    <label for="transferUsername">Recipient Username:</label>
                    <input type="text" id="transferUsername" required>
                </div>
                <div class="credit-form-group">
                    <label for="transferAmount">Amount (USD):</label>
                    <input type="number" id="transferAmount" min="1" max="1000" step="0.01" required>
                </div>
                <div class="credit-form-group">
                    <label for="transferMessage">Message (Optional):</label>
                    <textarea id="transferMessage" rows="3" placeholder="Optional message to recipient"></textarea>
                </div>
                <div class="credit-form-group">
                    <button type="submit" style="width: 100%;">Send Transfer</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Transaction History Modal -->
    <div id="historyModal" class="credit-modal">
        <div class="credit-modal-content" style="max-width: 800px;">
            <span class="close-credit-modal" onclick="closeHistoryModal()">&times;</span>
            <h3>📊 Transaction History</h3>
            <div id="transactionsList">
                <!-- Transactions will be loaded here -->
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button id="loadMoreTransactions" onclick="loadMoreTransactions()">Load More</button>
            </div>
        </div>
    </div>

    <script src="../JS/custom-modal.js"></script>
    <script>
        // All consolidated JS logic will go inside this single script block and DOMContentLoaded listener.
        document.addEventListener('DOMContentLoaded', function() {
            
            const i18n = <?= json_encode($translations, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE) ?>;

            // --- Element Cache ---
            const vipCheckbox = document.getElementById('service1');
            const vipConfigContainer = document.getElementById('vipConfigContainer');
            const vipOptionsContent = document.getElementById('vipOptionsContent');
            const modeButtons = document.querySelectorAll('.mode-btn');
            const orderForm = document.getElementById('orderForm');
            const pcOrdersList = document.getElementById('pcOrdersList');
            const ordersList = document.getElementById('ordersList');

            // Credit System Elements
            const walletBalance = document.getElementById('walletBalance');
            const totalDeposited = document.getElementById('totalDeposited');
            const totalSpent = document.getElementById('totalSpent');
            const frozenBalance = document.getElementById('frozenBalance');
            const depositModal = document.getElementById('depositModal');
            const transferModal = document.getElementById('transferModal');
            const historyModal = document.getElementById('historyModal');

            // Credit System Variables
            let currentTransactionPage = 1;
            let selectedPaymentMethod = null;

            // --- Generic Helper Functions ---
            function escapeHTML(str) {
                if (str === null || str === undefined) return '';
                return str.toString().replace(/[&<>"']/g, match => ({
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#39;'
                }[match]));
            }

            // --- VIP PC Customization Logic ---
            if (vipCheckbox) {
                vipCheckbox.addEventListener('change', () => {
                    if (vipCheckbox.checked) {
                        vipConfigContainer.style.display = 'block';
                    } else {
                        vipConfigContainer.style.display = 'none';
                        resetVipOptions();
                    }
                });
            }

            modeButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    modeButtons.forEach(btn => btn.classList.remove('active'));
                    e.target.classList.add('active');
                    const mode = e.target.dataset.mode;

                    if (mode === 'simple') {
                        renderSimpleModeOptions();
                    } else {
                        vipOptionsContent.innerHTML = '';
                    }
                });
            });
            
            function resetVipOptions() {
                if(vipOptionsContent) vipOptionsContent.innerHTML = '';
                modeButtons.forEach(btn => btn.classList.remove('active'));
            }

            function renderSimpleModeOptions() {
                if(!vipOptionsContent) return;
                vipOptionsContent.innerHTML = `
                    <div class="option-group">
                        <h4>${i18n.vip_pc_color_title}</h4>
                        <div class="radio-group"><label><input type="radio" name="vip_color" value="White" checked> ${i18n.vip_pc_color_white}</label><label><input type="radio" name="vip_color" value="Black"> ${i18n.vip_pc_color_black}</label></div>
                    </div>
                    <div class="option-group">
                        <h4>${i18n.vip_pc_size_title}</h4>
                        <div class="radio-group"><label><input type="radio" name="vip_size" value="Large" checked> ${i18n.vip_pc_size_large}</label><label><input type="radio" name="vip_size" value="Standard"> ${i18n.vip_pc_size_standard}</label><label><input type="radio" name="vip_size" value="Small"> ${i18n.vip_pc_size_small}</label></div>
                    </div>
                    <div class="option-group">
                        <h4>${i18n.vip_pc_use_title}</h4>
                        <div class="radio-group"><label><input type="radio" name="vip_purpose" value="Gaming" checked> ${i18n.vip_pc_use_gaming}</label><label><input type="radio" name="vip_purpose" value="Video Editing"> ${i18n.vip_pc_use_video_editing}</label><label><input type="radio" name="vip_purpose" value="Both"> ${i18n.vip_pc_use_both}</label></div>
                    </div>
                    <div class="option-group">
                        <h4>${i18n.vip_pc_tier_title}</h4>
                        <div class="radio-group">
                            <label><input type="radio" name="vip_tier" value="Entry" data-price="3000"> ${i18n.vip_pc_tier_entry} - $3000</label>
                            <label><input type="radio" name="vip_tier" value="Mid-Range" data-price="3500"> ${i18n.vip_pc_tier_mid} - $3500</label>
                            <label><input type="radio" name="vip_tier" value="High-End" data-price="4500"> ${i18n.vip_pc_tier_high} - $4500</label>
                            <label><input type="radio" name="vip_tier" value="Extreme" data-price="6400"> ${i18n.vip_pc_tier_extreme} - $6400</label>
                        </div>
                    </div>`;
            }

            // --- Main Form Submission Logic ---
            if (orderForm) {
                orderForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    const selectedServices = Array.from(document.querySelectorAll('input[name="service"]:checked')).map(cb => cb.value);
                    if (selectedServices.length === 0) {
                        showError(i18n.js_error_no_service_title, i18n.js_error_no_service_msg);
                        return;
                    }
                    const notes = document.getElementById('order-notes').value;
                    
                    if (selectedServices.length === 1 && selectedServices[0] === 'VIP Custom PC') {
                        const simpleModeBtn = document.querySelector('.mode-btn[data-mode="simple"]');
                        if (!simpleModeBtn || !simpleModeBtn.classList.contains('active')) {
                            showError(i18n.js_error_config_required_title, i18n.js_error_config_required_msg);
                            return;
                        }
                        const vipOptions = {
                            color: document.querySelector('input[name="vip_color"]:checked')?.value,
                            size: document.querySelector('input[name="vip_size"]:checked')?.value,
                            purpose: document.querySelector('input[name="vip_purpose"]:checked')?.value,
                            tier: document.querySelector('input[name="vip_tier"]:checked')?.value,
                            estimated_price: document.querySelector('input[name="vip_tier"]:checked')?.dataset.price
                        };
                         if (Object.values(vipOptions).some(val => !val)) {
                             showError(i18n.js_error_all_options_required_title, i18n.js_error_all_options_required_msg);
                             return;
                        }
                        const orderDetails = `- Color: ${vipOptions.color}\n- Size: ${vipOptions.size}\n- Primary Use: ${vipOptions.purpose}\n- Performance Tier: ${vipOptions.tier} (Est. $${vipOptions.estimated_price})\n- Additional Notes: ${notes || 'None'}`.trim();

                        const pcFormData = new FormData();
                        pcFormData.append('order_details', orderDetails);
                        
                        showConfirm(i18n.pc_order_form_title, "Submit this PC configuration for a quote?", () => {
                             showLoading(i18n.js_submitting_order_title, i18n.js_submitting_order_msg);
                            fetch('submit_pc_order.php', { method: 'POST', body: pcFormData })
                            .then(res => res.json())
                            .then(data => {
                                hideModal();
                                if(data.success){
                                    showSuccess('Request Sent!', 'Your PC quote request has been sent.', () => {
                                        orderForm.reset();
                                        if(vipConfigContainer) vipConfigContainer.style.display = 'none';
                                        resetVipOptions();
                                        loadUserOrders();
                                        loadPcOrders();
                                    });
                                } else {
                                    showError('Submission Failed', data.message || 'An unknown error occurred.');
                                }
                            }).catch(error => {
                                hideModal();
                                console.error('Error:', error);
                                showError('Network Error', 'Could not submit your request.');
                            });
                        });
                    } else {
                        const formData = new FormData();
                        if (selectedServices.includes('VIP Custom PC')) {
                             const vipOptions = {
                                mode: 'Simple',
                                color: document.querySelector('input[name="vip_color"]:checked')?.value,
                                size: document.querySelector('input[name="vip_size"]:checked')?.value,
                                purpose: document.querySelector('input[name="vip_purpose"]:checked')?.value,
                                tier: document.querySelector('input[name="vip_tier"]:checked')?.value,
                                estimated_price: document.querySelector('input[name="vip_tier"]:checked')?.dataset.price
                            };
                            formData.append('vip_customization', JSON.stringify(vipOptions));
                        }
                        showConfirm( i18n.js_confirm_order_title, i18n.js_confirm_order_msg.replace('{count}', selectedServices.length), () => {
                            showLoading(i18n.js_submitting_order_title, i18n.js_submitting_order_msg);
                            formData.append('services', JSON.stringify(selectedServices));
                            formData.append('notes', notes);
                            fetch('submit_order.php', { method: 'POST', body: formData })
                            .then(response => response.json())
                            .then(data => {
                                hideModal();
                                if (data.success) {
                                    showSuccess(i18n.js_order_success_title, i18n.js_order_success_msg, () => {
                                        orderForm.reset();
                                        if(vipConfigContainer) vipConfigContainer.style.display = 'none';
                                        resetVipOptions();
                                        loadUserOrders();
                                        loadPcOrders();
                                    });
                                } else {
                                    showError(i18n.js_order_failed_title, data.message || i18n.js_order_failed_msg);
                                }
                            })
                            .catch(error => {
                                hideModal();
                                console.error('Error:', error);
                                showError(i18n.js_order_failed_title, i18n.js_network_error_msg);
                            });
                        });
                    }
                });
            }

            // --- Logout & Session Logic ---
            document.getElementById('logoutBtn').addEventListener('click', () => {
                showConfirm(i18n.js_confirm_logout_title, i18n.js_confirm_logout_msg, () => {
                    showLoading(i18n.js_logging_out_title, i18n.js_logging_out_msg);
                    setTimeout(() => { window.location.href = 'logout.php'; }, 1000);
                });
            });
            setInterval(() => {
                fetch('heartbeat.php', { method: 'POST' })
                    .then(res => { if (res.status === 401) window.location.reload(); })
                    .catch(error => console.error('Heartbeat failed:', error));
            }, 60000);
            window.addEventListener('unload', () => { navigator.sendBeacon('log_offline.php', new Blob()); }, false);

            
            // --- Generic Order Functions ---
            function loadUserOrders() {
                fetch('get_user_orders.php')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) { displayOrders(data.orders); }
                        else { if(ordersList) ordersList.innerHTML = `<p>${i18n.js_no_orders_found}</p>`; }
                    })
                    .catch(error => {
                        console.error('Error loading orders:', error);
                        if(ordersList) ordersList.innerHTML = `<p>${i18n.js_error_loading_orders}</p>`;
                    });
            }

            function displayOrders(orders) {
                if (!ordersList) return;
                if (orders.length === 0) { ordersList.innerHTML = `<p>${i18n.js_no_orders_yet}</p>`; return; }
                let ordersHTML = '';
                orders.forEach(order => {
                    const services = JSON.parse(order.services).map(key => i18n[key.toLowerCase().replace(/ & /g, '_').replace(/ /g, '_')] || key);
                    const canCancel = order.status === 'pending';
                    let priceInfo = '';
                    if (order.estimated_price) priceInfo += `<p><strong>${i18n.js_estimated_price}:</strong> $${parseFloat(order.estimated_price).toFixed(2)}</p>`;
                    if (order.final_price) priceInfo += `<p><strong>${i18n.js_final_price}:</strong> $${parseFloat(order.final_price).toFixed(2)}</p>`;
                    let customInfo = '';
                    if (order.vip_customization) {
                        const vip = JSON.parse(order.vip_customization);
                        customInfo = `<div class="vip-details"><strong>${i18n.service_vip_pc} (${i18n.vip_pc_simple_mode_button}):</strong><ul>
                                        <li><strong>${i18n.vip_pc_color_title}:</strong> ${i18n['vip_pc_color_' + vip.color.toLowerCase()]}</li>
                                        <li><strong>${i18n.vip_pc_size_title}:</strong> ${i18n['vip_pc_size_' + vip.size.toLowerCase()]}</li>
                                        <li><strong>${i18n.vip_pc_use_title}:</strong> ${i18n['vip_pc_use_' + vip.purpose.toLowerCase().replace(/ /g, '_')]}</li>
                                        <li><strong>${i18n.vip_pc_tier_title}:</strong> ${i18n['vip_pc_tier_' + vip.tier.toLowerCase().split('-')[0]]}</li>
                                    </ul></div>`;
                        priceInfo = `<p><strong>${i18n.js_estimated_price}:</strong> $${parseFloat(vip.estimated_price).toFixed(2)}</p>`;
                    }
                    ordersHTML += `<div class="order-card"><h4>Order #${order.id}</h4><p><strong>Date:</strong> ${new Date(order.created_at).toLocaleDateString()}</p><p><strong>${i18n.js_order_status}:</strong> <span class="order-status ${order.status}">${getStatusText(order.status)}</span></p>${priceInfo}<p><strong>${i18n.js_services}:</strong></p><ul>${services.map(s => `<li>${s}</li>`).join('')}</ul>${customInfo}<p><strong>${i18n.js_notes}:</strong> ${escapeHTML(order.notes) || 'None'}</p><div class="order-actions">${canCancel ? `<button onclick="cancelOrder(${order.id})">${i18n.js_cancel_order_button}</button>` : ''}</div></div>`;
                });
                ordersList.innerHTML = ordersHTML;
            }

            function getStatusText(status) {
                const map = { 'pending': i18n.js_order_status_pending, 'processing': i18n.js_order_status_processing, 'completed': i18n.js_order_status_completed, 'cancelled': i18n.js_order_status_cancelled };
                return map[status] || status;
            }

            window.cancelOrder = function(orderId) { 
                showConfirm(i18n.js_cancel_confirm_title, i18n.js_cancel_confirm_msg, () => {
                    showLoading(i18n.js_cancelling_order_title, i18n.js_cancelling_order_msg);
                    const fd = new FormData();
                    fd.append('order_id', orderId);
                    fetch('cancel_order.php', { method: 'POST', body: fd })
                    .then(res => res.json())
                    .then(data => {
                        hideModal();
                        if (data.success) { showSuccess(i18n.js_cancel_success_title, i18n.js_cancel_success_msg, loadUserOrders);
                        } else { showError(i18n.js_cancel_failed_title, data.message || i18n.js_cancel_failed_msg); }
                    })
                    .catch(err => {
                        hideModal();
                        console.error('Error:', err);
                        showError(i18n.js_cancel_failed_title, i18n.js_network_error_msg);
                    });
                });
            }

            // --- PC Order Functions ---
            function loadPcOrders() {
                if (!pcOrdersList) return;
                fetch('get_member_orders.php?t=' + new Date().getTime())
                    .then(res => res.json())
                    .then(data => {
                        if (data.success) { renderPcOrders(data.orders); } 
                        else { pcOrdersList.innerHTML = `<p>${data.message || 'Could not load PC orders.'}</p>`; }
                    })
                    .catch(err => {
                        console.error('Error fetching PC orders:', err);
                        pcOrdersList.innerHTML = `<p>An error occurred while fetching your PC orders.</p>`;
                    });
            }
            
            function renderPcOrders(orders) {
                if (!pcOrdersList) return;
                if (orders.length === 0) { pcOrdersList.innerHTML = `<p>${i18n.no_pc_orders_found || 'No PC orders found.'}</p>`; return; }
                pcOrdersList.innerHTML = orders.map(order => {
                    const statusClass = 'pc-order-status status-' + order.status.toLowerCase().replace(/ /g, '-');
                    const content = generateOrderContent(order);
                    return `<div class="pc-order-card" data-order-id="${order.id}">
                        <h4>${i18n.pc_order_card_title || 'PC Order'} #${order.id}</h4>
                        <div class="pc-order-details-grid">
                            <p><strong>${i18n.order_status || 'Status'}:</strong></p><p><span class="${statusClass}">${order.status}</span></p>
                            <p><strong>${i18n.order_date || 'Date'}:</strong></p><p>${new Date(order.created_at).toLocaleString()}</p>
                            <p><strong>${i18n.order_details || 'Details'}:</strong></p><p style="white-space: pre-wrap;">${escapeHTML(order.order_details)}</p>
                            ${order.final_price ? `<p><strong>${i18n.final_price || 'Final Price'}:</strong></p><p>$${parseFloat(order.final_price).toFixed(2)}</p>` : ''}
                            ${order.estimated_completion_date ? `<p><strong>${i18n.eta || 'ETA'}:</strong></p><p>${new Date(order.estimated_completion_date).toLocaleDateString()}</p>` : ''}
                        </div>
                        <div class="pc-order-notices">${content.notice}</div>
                        <div class="pc-order-actions">${content.buttons}</div>
                    </div>`;
                }).join('');
            }

            function generateOrderContent(order) {
                let buttons = '', notice = '';
                const canCancel = ['Quote Provided', 'Order Pending'].includes(order.status);
                const isPaid = ['Payment Received', 'Order in Progress', 'Order Shipped', 'Order Delivered'].includes(order.status);
                if (canCancel) { notice = `<div class="pc-order-notices notice-warning">${i18n.pc_order_no_refund_warning}</div>`; }
                else if (isPaid) { notice = `<div class="pc-order-notices notice-info">${i18n.pc_order_in_progress_notice}</div>`; }
                switch (order.status) {
                    case 'Quote Provided': buttons += `<button class="confirm-btn" data-action="confirm_quote">${i18n.button_confirm_quote || 'Confirm Quote'}</button> `; break;
                    case 'Order Pending':  buttons += `<button class="confirm-btn" data-action="make_payment">${i18n.button_pay_now || 'Pay Now'}</button> `; break;
                    case 'Order Shipped':  buttons += `<button class="confirm-btn" data-action="confirm_delivery">${i18n.button_confirm_delivery || 'Confirm Delivery'}</button>`; break;
                }
                if (canCancel) { buttons += `<button class="cancel-btn" data-action="cancel_order">${i18n.button_cancel_order || 'Cancel Order'}</button>`; }
                return { buttons, notice };
            }
            
            if (pcOrdersList) {
                pcOrdersList.addEventListener('click', function(e) {
                    if (e.target.tagName === 'BUTTON') {
                        const action = e.target.dataset.action;
                        if (!action) return;
                        const card = e.target.closest('.pc-order-card');
                        handleOrderAction(card.dataset.orderId, action, e.target);
                    }
                });
            }

            function handleOrderAction(orderId, action, button) {
                if (action === 'make_payment') {
                    showSuccess('Redirecting to Payment...', 'You will now be redirected to complete your payment.');
                    setTimeout(() => updateOrderStatus(orderId, action, button), 2000);
                    return;
                }
                const originalButtonText = button.innerHTML; // Store original HTML content
                let confirmTitle = i18n.js_confirm_action_title || 'Confirm Action';
                let confirmMsg = i18n.js_confirm_action_msg || 'Are you sure you want to proceed?';
                if (action === 'cancel_order') {
                    confirmTitle = i18n.js_cancel_confirm_title || 'Cancel Order';
                    confirmMsg = i18n.pc_order_cancellation_confirmation || 'Are you sure you want to cancel this order?';
                } else if (action === 'confirm_delivery') {
                    confirmTitle = i18n.button_confirm_delivery || 'Confirm Delivery';
                    confirmMsg = i18n.pc_order_delivery_confirmation_warning || 'Please confirm that you have received your order.';
                }
                
                showConfirm(confirmTitle, confirmMsg, () => {
                    updateOrderStatus(orderId, action, button, originalButtonText);
                });
            }

            function updateOrderStatus(orderId, action, button, originalButtonText) {
                button.disabled = true; button.textContent = 'Processing...';
                const formData = new FormData();
                formData.append('order_id', orderId); formData.append('action', action);
                fetch('update_pc_order_status.php', { method: 'POST', body: formData })
                .then(res => res.json())
                .then(data => {
                    if (data.success) { showSuccess('Success', 'The order has been updated.', loadPcOrders); } 
                    else { showError('Error', data.message || 'An unknown error occurred.'); }
                })
                .catch(err => {
                    console.error("Update failed:", err);
                    showError('Network Error', 'The update could not be completed.');
                })
                .finally(() => {
                    button.disabled = false;
                    // Restore original content, not just text
                    button.innerHTML = originalButtonText; 
                });
            }

            // --- KMS Credit System Functions ---

            // Load wallet information
            function loadWalletInfo() {
                fetch('credit_wallet.php?action=get_wallet')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            walletBalance.textContent = '$' + data.wallet.balance;
                            totalDeposited.textContent = '$' + data.wallet.total_deposited;
                            totalSpent.textContent = '$' + data.wallet.total_spent;
                            frozenBalance.textContent = '$' + data.wallet.frozen_balance;
                        } else {
                            console.error('Failed to load wallet info:', data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error loading wallet info:', error);
                    });
            }

            // Load payment methods
            function loadPaymentMethods() {
                fetch('credit_wallet.php?action=get_payment_methods')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const grid = document.getElementById('paymentMethodGrid');
                            grid.innerHTML = '';

                            data.payment_methods.forEach(method => {
                                const option = document.createElement('div');
                                option.className = 'payment-method-option';
                                option.dataset.method = method.method_name;
                                option.innerHTML = `
                                    <h4>${method.display_name}</h4>
                                    <p>Min: $${method.min_amount} - Max: $${method.max_amount}</p>
                                    <p>Fee: ${method.fee_percentage}% + $${method.fee_fixed}</p>
                                `;
                                option.onclick = () => selectPaymentMethod(method.method_name, option);
                                grid.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error loading payment methods:', error);
                    });
            }

            // Select payment method
            function selectPaymentMethod(methodName, element) {
                document.querySelectorAll('.payment-method-option').forEach(el => el.classList.remove('selected'));
                element.classList.add('selected');
                selectedPaymentMethod = methodName;
            }

            // Open deposit modal
            window.openDepositModal = function() {
                loadPaymentMethods();
                depositModal.style.display = 'block';
            }

            // Close deposit modal
            window.closeDepositModal = function() {
                depositModal.style.display = 'none';
                document.getElementById('depositForm').reset();
                selectedPaymentMethod = null;
                document.querySelectorAll('.payment-method-option').forEach(el => el.classList.remove('selected'));
            }

            // Open transfer modal
            window.openTransferModal = function() {
                transferModal.style.display = 'block';
            }

            // Close transfer modal
            window.closeTransferModal = function() {
                transferModal.style.display = 'none';
                document.getElementById('transferForm').reset();
            }

            // Open transaction history modal
            window.openTransactionHistory = function() {
                currentTransactionPage = 1;
                loadTransactionHistory();
                historyModal.style.display = 'block';
            }

            // Close transaction history modal
            window.closeHistoryModal = function() {
                historyModal.style.display = 'none';
            }

            // Load transaction history
            function loadTransactionHistory(append = false) {
                fetch(`credit_wallet.php?action=get_transactions&page=${currentTransactionPage}&limit=10`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const container = document.getElementById('transactionsList');

                            if (!append) {
                                container.innerHTML = '';
                            }

                            data.transactions.forEach(transaction => {
                                const card = document.createElement('div');
                                card.className = 'transaction-card';
                                card.innerHTML = `
                                    <div class="transaction-header">
                                        <span class="transaction-type">${transaction.type_display}</span>
                                        <span class="transaction-amount ${transaction.amount_display.startsWith('+') ? 'positive' : 'negative'}">${transaction.amount_display}</span>
                                    </div>
                                    <div class="transaction-details">${transaction.description || 'No description'}</div>
                                    <div class="transaction-date">${transaction.created_at}</div>
                                `;
                                container.appendChild(card);
                            });

                            // Show/hide load more button
                            const loadMoreBtn = document.getElementById('loadMoreTransactions');
                            loadMoreBtn.style.display = data.pagination.has_more ? 'block' : 'none';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading transactions:', error);
                    });
            }

            // Load more transactions
            window.loadMoreTransactions = function() {
                currentTransactionPage++;
                loadTransactionHistory(true);
            }

            // Handle deposit form submission
            document.getElementById('depositForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const amount = document.getElementById('depositAmount').value;

                if (!selectedPaymentMethod) {
                    showError('Error', 'Please select a payment method');
                    return;
                }

                if (!amount || amount <= 0) {
                    showError('Error', 'Please enter a valid amount');
                    return;
                }

                showLoading('Processing', 'Creating deposit request...');

                const formData = new FormData();
                formData.append('action', 'create_deposit');
                formData.append('amount', amount);
                formData.append('payment_method', selectedPaymentMethod);

                fetch('credit_deposit.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    hideModal();
                    if (data.success) {
                        closeDepositModal();
                        showSuccess('Success', `Deposit request created successfully! Transaction ID: ${data.transaction_id}`, () => {
                            loadWalletInfo();
                        });
                    } else {
                        showError('Error', data.message);
                    }
                })
                .catch(error => {
                    hideModal();
                    console.error('Error creating deposit:', error);
                    showError('Error', 'Failed to create deposit request');
                });
            });

            // Handle transfer form submission
            document.getElementById('transferForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const username = document.getElementById('transferUsername').value;
                const amount = document.getElementById('transferAmount').value;
                const message = document.getElementById('transferMessage').value;

                if (!username || !amount || amount <= 0) {
                    showError('Error', 'Please fill in all required fields');
                    return;
                }

                showLoading('Processing', 'Processing transfer...');

                const formData = new FormData();
                formData.append('action', 'transfer');
                formData.append('to_username', username);
                formData.append('amount', amount);
                formData.append('message', message);

                fetch('credit_wallet.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    hideModal();
                    if (data.success) {
                        closeTransferModal();
                        showSuccess('Success', 'Transfer completed successfully!', () => {
                            loadWalletInfo();
                        });
                    } else {
                        showError('Error', data.message);
                    }
                })
                .catch(error => {
                    hideModal();
                    console.error('Error processing transfer:', error);
                    showError('Error', 'Failed to process transfer');
                });
            });

            // --- Initial Page Load ---
            loadUserOrders();
            loadPcOrders();
            loadWalletInfo();

        });
    </script>
</body>
</html>