<?php
/**
 * Stripe Payment Redirect Page
 * This is a demo implementation for Stripe integration
 */

require_once 'config.php';
require_once 'credit_system.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit;
}

$transaction_id = $_GET['transaction_id'] ?? '';

if (empty($transaction_id)) {
    die('Invalid transaction ID');
}

// Get deposit details
$sql = "SELECT dr.*, ct.user_id FROM deposit_records dr 
        JOIN credit_transactions ct ON dr.transaction_id = ct.transaction_id 
        WHERE dr.transaction_id = ? AND dr.user_id = ?";
$stmt = execute_query($link, $sql, "si", [$transaction_id, $_SESSION['user_id']]);

if (!$stmt) {
    die('Database error');
}

$result = mysqli_stmt_get_result($stmt);
$deposit = mysqli_fetch_assoc($result);
mysqli_stmt_close($stmt);

if (!$deposit) {
    die('Deposit not found');
}

if ($deposit['payment_status'] !== 'pending') {
    die('Deposit already processed');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stripe Payment - KMS Credit</title>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        body { font-family: Arial, sans-serif; background-color: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #635bff; text-align: center; }
        .payment-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .payment-details h3 { margin-top: 0; color: #333; }
        .detail-row { display: flex; justify-content: space-between; margin: 10px 0; }
        .detail-row strong { color: #333; }
        .card-element { background: white; padding: 12px; border: 1px solid #ccc; border-radius: 4px; margin: 10px 0; }
        .buttons { text-align: center; margin-top: 30px; }
        .btn { padding: 12px 30px; margin: 0 10px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; text-decoration: none; display: inline-block; }
        .btn-primary { background-color: #635bff; color: white; }
        .btn-primary:hover { background-color: #5a52e8; }
        .btn-primary:disabled { background-color: #ccc; cursor: not-allowed; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-secondary:hover { background-color: #5a6268; }
        .demo-notice { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .demo-notice h4 { margin-top: 0; color: #856404; }
        .error-message { color: #dc3545; margin-top: 10px; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Stripe Payment</h1>
        
        <div class="demo-notice">
            <h4>🚧 Demo Mode</h4>
            <p>This is a demonstration of Stripe integration. Use test card number: <strong>4242 4242 4242 4242</strong></p>
            <p>Use any future expiry date, any 3-digit CVC, and any postal code.</p>
        </div>
        
        <div class="payment-details">
            <h3>Payment Details</h3>
            <div class="detail-row">
                <span>Transaction ID:</span>
                <strong><?= htmlspecialchars($transaction_id) ?></strong>
            </div>
            <div class="detail-row">
                <span>Amount:</span>
                <strong>$<?= number_format($deposit['amount'], 2) ?></strong>
            </div>
            <div class="detail-row">
                <span>Payment Method:</span>
                <strong>Credit Card (Stripe)</strong>
            </div>
            <div class="detail-row">
                <span>Merchant:</span>
                <strong>KelvinKMS.com</strong>
            </div>
        </div>
        
        <form id="payment-form">
            <div class="form-group">
                <label for="card-element">Credit or Debit Card</label>
                <div id="card-element" class="card-element">
                    <!-- Stripe Elements will create form elements here -->
                </div>
                <div id="card-errors" class="error-message" role="alert"></div>
            </div>
            
            <div class="buttons">
                <button type="submit" id="submit-button" class="btn btn-primary">
                    Pay $<?= number_format($deposit['amount'], 2) ?>
                </button>
                <button type="button" class="btn btn-secondary" onclick="simulateCancel()">Cancel Payment</button>
                <a href="member.php" class="btn btn-secondary">Back to Dashboard</a>
            </div>
        </form>
    </div>

    <script>
        // Demo mode - simulate Stripe integration
        // In real implementation, you would use your Stripe publishable key
        const DEMO_MODE = true;
        
        if (DEMO_MODE) {
            // Demo implementation
            document.getElementById('card-element').innerHTML = `
                <div style="padding: 10px; background: #f8f9fa; border-radius: 4px; text-align: center;">
                    <strong>Demo Mode</strong><br>
                    Click "Pay" to simulate successful payment
                </div>
            `;
            
            document.getElementById('payment-form').addEventListener('submit', function(e) {
                e.preventDefault();
                simulateStripePayment();
            });
        } else {
            // Real Stripe implementation
            const stripe = Stripe('pk_test_your_publishable_key_here'); // Replace with your key
            const elements = stripe.elements();
            
            const cardElement = elements.create('card');
            cardElement.mount('#card-element');
            
            cardElement.on('change', function(event) {
                const displayError = document.getElementById('card-errors');
                if (event.error) {
                    displayError.textContent = event.error.message;
                } else {
                    displayError.textContent = '';
                }
            });
            
            document.getElementById('payment-form').addEventListener('submit', function(e) {
                e.preventDefault();
                handleStripePayment(stripe, cardElement);
            });
        }
        
        function simulateStripePayment() {
            const submitButton = document.getElementById('submit-button');
            submitButton.disabled = true;
            submitButton.textContent = 'Processing...';
            
            // Simulate processing delay
            setTimeout(() => {
                const formData = new FormData();
                formData.append('action', 'simulate_stripe_success');
                formData.append('transaction_id', '<?= $transaction_id ?>');
                formData.append('external_payment_id', 'STRIPE_' + Date.now());
                
                fetch('stripe_callback.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Payment successful! Your credit has been added to your account.');
                        window.location.href = 'member.php';
                    } else {
                        alert('Payment processing failed: ' + data.message);
                        submitButton.disabled = false;
                        submitButton.textContent = 'Pay $<?= number_format($deposit['amount'], 2) ?>';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Network error occurred');
                    submitButton.disabled = false;
                    submitButton.textContent = 'Pay $<?= number_format($deposit['amount'], 2) ?>';
                });
            }, 2000);
        }
        
        function handleStripePayment(stripe, cardElement) {
            const submitButton = document.getElementById('submit-button');
            submitButton.disabled = true;
            submitButton.textContent = 'Processing...';
            
            // Create payment method
            stripe.createPaymentMethod({
                type: 'card',
                card: cardElement,
            }).then(function(result) {
                if (result.error) {
                    // Show error to customer
                    document.getElementById('card-errors').textContent = result.error.message;
                    submitButton.disabled = false;
                    submitButton.textContent = 'Pay $<?= number_format($deposit['amount'], 2) ?>';
                } else {
                    // Send payment method to server
                    processPayment(result.paymentMethod.id);
                }
            });
        }
        
        function processPayment(paymentMethodId) {
            const formData = new FormData();
            formData.append('action', 'process_stripe_payment');
            formData.append('transaction_id', '<?= $transaction_id ?>');
            formData.append('payment_method_id', paymentMethodId);
            
            fetch('stripe_callback.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Payment successful! Your credit has been added to your account.');
                    window.location.href = 'member.php';
                } else {
                    alert('Payment processing failed: ' + data.message);
                    document.getElementById('submit-button').disabled = false;
                    document.getElementById('submit-button').textContent = 'Pay $<?= number_format($deposit['amount'], 2) ?>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Network error occurred');
                document.getElementById('submit-button').disabled = false;
                document.getElementById('submit-button').textContent = 'Pay $<?= number_format($deposit['amount'], 2) ?>';
            });
        }
        
        function simulateCancel() {
            if (confirm('Cancel this payment?')) {
                const formData = new FormData();
                formData.append('action', 'cancel_deposit');
                formData.append('transaction_id', '<?= $transaction_id ?>');
                
                fetch('credit_deposit.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Payment cancelled.');
                        window.location.href = 'member.php';
                    } else {
                        alert('Error cancelling payment: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Network error occurred');
                });
            }
        }
    </script>
</body>
</html>
