-- KMS Credit System Database Setup
-- This script creates all necessary tables for the KMS Credit $ system
-- Run this script after the main database_setup.sql

USE kelvinkms;

-- User Wallets Table - Stores each user's credit balance
CREATE TABLE IF NOT EXISTS user_wallets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    frozen_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Balance locked during pending transactions',
    total_deposited DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Total amount ever deposited',
    total_spent DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Total amount ever spent',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_balance (balance)
);

-- Credit Transactions Table - Records all credit-related transactions
CREATE TABLE IF NOT EXISTS credit_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL COMMENT 'Unique transaction identifier',
    user_id INT NOT NULL,
    transaction_type ENUM('deposit', 'withdraw', 'spend', 'refund', 'transfer_in', 'transfer_out', 'admin_adjust') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    balance_before DECIMAL(10,2) NOT NULL,
    balance_after DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    description TEXT,
    reference_type ENUM('order', 'deposit', 'transfer', 'admin', 'refund') DEFAULT NULL,
    reference_id INT DEFAULT NULL COMMENT 'ID of related order, deposit, etc.',
    payment_method VARCHAR(50) DEFAULT NULL COMMENT 'PayPal, Stripe, etc.',
    external_transaction_id VARCHAR(100) DEFAULT NULL COMMENT 'Payment gateway transaction ID',
    admin_user_id INT DEFAULT NULL COMMENT 'Admin who processed the transaction',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_reference (reference_type, reference_id)
);

-- Deposit Records Table - Detailed records of all deposits
CREATE TABLE IF NOT EXISTS deposit_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    transaction_id VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    payment_method ENUM('paypal', 'stripe', 'bank_transfer', 'crypto', 'admin') NOT NULL,
    payment_status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    external_payment_id VARCHAR(100) DEFAULT NULL COMMENT 'Payment gateway transaction ID',
    payment_details JSON DEFAULT NULL COMMENT 'Additional payment information',
    admin_notes TEXT DEFAULT NULL,
    processed_by INT DEFAULT NULL COMMENT 'Admin user who processed manual deposits',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (transaction_id) REFERENCES credit_transactions(transaction_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_payment_method (payment_method),
    INDEX idx_created_at (created_at)
);

-- Transfer Records Table - Records of credit transfers between users
CREATE TABLE IF NOT EXISTS transfer_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transfer_id VARCHAR(50) UNIQUE NOT NULL,
    from_user_id INT NOT NULL,
    to_user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    message TEXT DEFAULT NULL COMMENT 'Optional message from sender',
    admin_approved BOOLEAN DEFAULT FALSE COMMENT 'Whether admin approval is required',
    approved_by INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (from_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (to_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_from_user (from_user_id),
    INDEX idx_to_user (to_user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Payment Methods Configuration Table
CREATE TABLE IF NOT EXISTS payment_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    method_name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    min_amount DECIMAL(10,2) DEFAULT 1.00,
    max_amount DECIMAL(10,2) DEFAULT 10000.00,
    fee_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Fee as percentage of transaction',
    fee_fixed DECIMAL(10,2) DEFAULT 0.00 COMMENT 'Fixed fee amount',
    config JSON DEFAULT NULL COMMENT 'Method-specific configuration',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active)
);

-- System Settings for Credit System
CREATE TABLE IF NOT EXISTS credit_system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT DEFAULT NULL,
    updated_by INT DEFAULT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Initialize default payment methods
INSERT INTO payment_methods (method_name, display_name, is_active, min_amount, max_amount, fee_percentage, fee_fixed) VALUES
('paypal', 'PayPal', TRUE, 5.00, 5000.00, 2.90, 0.30),
('stripe', 'Credit Card (Stripe)', TRUE, 1.00, 10000.00, 2.90, 0.30),
('bank_transfer', 'Bank Transfer', TRUE, 10.00, 50000.00, 0.00, 0.00),
('admin', 'Admin Manual Deposit', TRUE, 0.01, 999999.99, 0.00, 0.00)
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    min_amount = VALUES(min_amount),
    max_amount = VALUES(max_amount);

-- Initialize system settings
INSERT INTO credit_system_settings (setting_key, setting_value, description) VALUES
('min_transfer_amount', '1.00', 'Minimum amount for user-to-user transfers'),
('max_transfer_amount', '1000.00', 'Maximum amount for user-to-user transfers'),
('daily_transfer_limit', '5000.00', 'Daily transfer limit per user'),
('require_transfer_approval', 'false', 'Whether transfers require admin approval'),
('system_fee_percentage', '0.00', 'System fee percentage for transactions'),
('maintenance_mode', 'false', 'Whether credit system is in maintenance mode')
ON DUPLICATE KEY UPDATE 
    setting_value = VALUES(setting_value);

-- Create wallet for existing users
INSERT INTO user_wallets (user_id, balance)
SELECT id, 0.00 FROM users 
WHERE id NOT IN (SELECT user_id FROM user_wallets)
ON DUPLICATE KEY UPDATE balance = balance;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_type ON credit_transactions(user_id, transaction_type);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_status_created ON credit_transactions(status, created_at);
CREATE INDEX IF NOT EXISTS idx_deposit_records_status_created ON deposit_records(payment_status, created_at);

-- Create a trigger to automatically create wallet for new users
DELIMITER //
CREATE TRIGGER IF NOT EXISTS create_user_wallet_trigger
    AFTER INSERT ON users
    FOR EACH ROW
BEGIN
    INSERT INTO user_wallets (user_id, balance) VALUES (NEW.id, 0.00);
END//
DELIMITER ;

-- Create a view for user wallet summary
CREATE OR REPLACE VIEW user_wallet_summary AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    uw.balance,
    uw.frozen_balance,
    uw.total_deposited,
    uw.total_spent,
    (uw.balance + uw.frozen_balance) as total_balance,
    uw.created_at as wallet_created_at,
    uw.updated_at as wallet_updated_at
FROM users u
LEFT JOIN user_wallets uw ON u.id = uw.user_id;

-- Create a view for transaction summary
CREATE OR REPLACE VIEW transaction_summary AS
SELECT 
    ct.id,
    ct.transaction_id,
    u.username,
    ct.transaction_type,
    ct.amount,
    ct.status,
    ct.description,
    ct.payment_method,
    ct.created_at
FROM credit_transactions ct
JOIN users u ON ct.user_id = u.id
ORDER BY ct.created_at DESC;

COMMIT;
